<?php

namespace <PERSON>haqi\FlyingPress\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use <PERSON><PERSON>qi\FlyingPress\Services\CacheService;
use <PERSON>haqi\FlyingPress\Services\OptimizationService;

class FlyingPressOptimizationMiddleware
{
    protected CacheService $cacheService;
    protected OptimizationService $optimizationService;

    public function __construct(CacheService $cacheService, OptimizationService $optimizationService)
    {
        $this->cacheService = $cacheService;
        $this->optimizationService = $optimizationService;
    }

    public function handle(Request $request, Closure $next)
    {
        // Skip optimization for admin routes
        if ($request->is('admin/*') || $request->is('api/*')) {
            return $next($request);
        }

        // Skip if license is not active
        if (!setting('flying_press_license_active', false)) {
            return $next($request);
        }

        // Check if we should serve from cache
        if ($this->cacheService->shouldCache()) {
            $cachedContent = $this->cacheService->getCachedPageContent($request->fullUrl());
            if ($cachedContent) {
                return response($cachedContent)
                    ->header('X-FlyingPress-Cache', 'HIT')
                    ->header('Cache-Control', 'public, max-age=3600');
            }
        }

        $response = $next($request);

        // Only optimize HTML responses
        if ($response instanceof Response && $this->isHtmlResponse($response)) {
            $content = $response->getContent();
            
            // Apply optimizations
            $optimizedContent = $this->optimizeContent($content);
            
            $response->setContent($optimizedContent);
            
            // Cache the optimized content
            if ($this->cacheService->shouldCache()) {
                $this->cacheService->cachePageContent($request->fullUrl(), $optimizedContent);
                $response->header('X-FlyingPress-Cache', 'MISS');
            }
            
            // Add performance headers
            $response->header('X-FlyingPress-Optimized', 'true');
        }

        return $response;
    }

    protected function isHtmlResponse(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        return strpos($contentType, 'text/html') !== false || empty($contentType);
    }

    protected function optimizeContent(string $content): string
    {
        // Apply various optimizations
        $content = $this->optimizationService->optimizeContent($content);
        
        // Minify CSS if enabled
        if (setting('flying_press_css_js_minify', false)) {
            $content = $this->minifyCss($content);
            $content = $this->minifyJs($content);
        }

        // Remove unused CSS if enabled
        if (setting('flying_press_css_rucss', false)) {
            $content = $this->removeUnusedCss($content);
        }

        return $content;
    }

    protected function minifyCss(string $content): string
    {
        return preg_replace_callback(
            '/<style[^>]*>(.*?)<\/style>/is',
            function ($matches) {
                $css = $matches[1];
                // Basic CSS minification
                $css = preg_replace('/\s+/', ' ', $css);
                $css = preg_replace('/;\s*}/', '}', $css);
                $css = preg_replace('/\s*{\s*/', '{', $css);
                $css = preg_replace('/;\s*/', ';', $css);
                return '<style>' . trim($css) . '</style>';
            },
            $content
        );
    }

    protected function minifyJs(string $content): string
    {
        return preg_replace_callback(
            '/<script[^>]*>(.*?)<\/script>/is',
            function ($matches) {
                $js = $matches[1];
                // Basic JS minification (be careful with this in production)
                $js = preg_replace('/\s+/', ' ', $js);
                $js = preg_replace('/;\s*/', ';', $js);
                return '<script>' . trim($js) . '</script>';
            },
            $content
        );
    }

    protected function removeUnusedCss(string $content): string
    {
        // This is a simplified implementation
        // In a real scenario, you would analyze which CSS rules are actually used
        return $content;
    }
}

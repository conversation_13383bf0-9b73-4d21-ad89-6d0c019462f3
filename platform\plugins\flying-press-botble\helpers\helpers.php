<?php

if (!function_exists('flying_press_is_active')) {
    /**
     * Check if FlyingPress is active and licensed
     */
    function flying_press_is_active(): bool
    {
        return setting('flying_press_license_active', false);
    }
}

if (!function_exists('flying_press_cache_clear')) {
    /**
     * Clear FlyingPress cache
     */
    function flying_press_cache_clear(): void
    {
        if (flying_press_is_active()) {
            app(\Shaqi\FlyingPress\Services\CacheService::class)->clearAll();
        }
    }
}

if (!function_exists('flying_press_optimize_url')) {
    /**
     * Optimize asset URL with CDN if enabled
     */
    function flying_press_optimize_url(string $url): string
    {
        if (flying_press_is_active()) {
            return app(\Shaqi\FlyingPress\Services\OptimizationService::class)->optimizeAssetUrl($url);
        }

        return $url;
    }
}

if (!function_exists('flying_press_should_lazy_load')) {
    /**
     * Check if lazy loading is enabled
     */
    function flying_press_should_lazy_load(): bool
    {
        return flying_press_is_active() && setting('flying_press_lazy_load', false);
    }
}

if (!function_exists('flying_press_get_license_status')) {
    /**
     * Get license status information
     */
    function flying_press_get_license_status(): array
    {
        return app(\Shaqi\FlyingPress\Services\LicenseService::class)->getLicenseStatus();
    }
}

if (!function_exists('flying_press_minify_html')) {
    /**
     * Minify HTML content
     */
    function flying_press_minify_html(string $content): string
    {
        if (!flying_press_is_active()) {
            return $content;
        }

        // Basic HTML minification
        $content = preg_replace('/\s+/', ' ', $content);
        $content = preg_replace('/>\s+</', '><', $content);
        return trim($content);
    }
}

if (!function_exists('flying_press_add_lazy_loading')) {
    /**
     * Add lazy loading attributes to images
     */
    function flying_press_add_lazy_loading(string $html): string
    {
        if (!flying_press_should_lazy_load()) {
            return $html;
        }

        return preg_replace_callback(
            '/<img([^>]+)>/i',
            function ($matches) {
                $img = $matches[0];

                // Skip if already has loading attribute
                if (strpos($img, 'loading=') !== false) {
                    return $img;
                }

                // Add loading="lazy"
                return str_replace('<img', '<img loading="lazy"', $img);
            },
            $html
        );
    }
}

if (!function_exists('flying_press_preload_font')) {
    /**
     * Generate font preload link
     */
    function flying_press_preload_font(string $fontUrl): string
    {
        if (!flying_press_is_active() || !setting('flying_press_fonts_preload', false)) {
            return '';
        }

        return '<link rel="preload" href="' . $fontUrl . '" as="font" type="font/woff2" crossorigin>';
    }
}

if (!function_exists('flying_press_critical_css')) {
    /**
     * Generate critical CSS for above-the-fold content
     */
    function flying_press_critical_css(): string
    {
        if (!flying_press_is_active() || !setting('flying_press_css_rucss', false)) {
            return '';
        }

        return '<style>
        /* Critical CSS for above-the-fold content */
        body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        h1, h2, h3 { margin: 0 0 1rem 0; line-height: 1.2; }
        img { max-width: 100%; height: auto; }
        </style>';
    }
}

if (!function_exists('flying_press_defer_js')) {
    /**
     * Add defer attribute to JavaScript
     */
    function flying_press_defer_js(string $html): string
    {
        if (!flying_press_is_active() || !setting('flying_press_js_delay', false)) {
            return $html;
        }

        return preg_replace_callback(
            '/<script([^>]+)>/i',
            function ($matches) {
                $script = $matches[0];

                // Skip if already has defer or async
                if (strpos($script, 'defer') !== false || strpos($script, 'async') !== false) {
                    return $script;
                }

                // Add defer attribute
                return str_replace('<script', '<script defer', $script);
            },
            $html
        );
    }
}

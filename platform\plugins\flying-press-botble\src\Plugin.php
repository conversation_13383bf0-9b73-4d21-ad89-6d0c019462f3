<?php

namespace <PERSON>haqi\FlyingPress;

use <PERSON><PERSON>qi\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;

class Plugin extends PluginOperationAbstract
{
    public static function activated(): void
    {
        // Set default configuration when plugin is activated
        $defaultConfig = [
            // License - pre-configured with valid license
            'flying_press_license_key' => 'B5E0B5F8DD8689E6ACA49DD6E6E1A930',
            'flying_press_license_active' => true,
            'flying_press_license_status' => 'active',

            // CSS & JavaScript Optimization
            'flying_press_css_js_minify' => true,
            'flying_press_css_rucss' => true,
            'flying_press_js_delay' => true,
            'flying_press_js_delay_method' => 'background',
            'flying_press_js_delay_third_party' => true,
            'flying_press_css_js_self_host_third_party' => true,

            // Image, Video & iFrame Optimization
            'flying_press_lazy_load' => true,
            'flying_press_properly_size_images' => true,
            'flying_press_youtube_placeholder' => true,
            'flying_press_self_host_gravatars' => true,

            // Fonts Optimization
            'flying_press_fonts_preload' => true,
            'flying_press_fonts_optimize_google' => true,
            'flying_press_fonts_display_swap' => true,

            // Rendering Optimization
            'flying_press_lazy_render' => true,

            // Basic Caching
            'flying_press_cache_link_prefetch' => true,
            'flying_press_cache_mobile' => false,
            'flying_press_cache_logged_in' => false,
            'flying_press_cache_refresh' => false,
            'flying_press_cache_refresh_interval' => '2hours',

            // CDN
            'flying_press_cdn' => false,
            'flying_press_cdn_type' => 'custom',
            'flying_press_cdn_url' => '',
            'flying_press_cdn_file_types' => 'all',

            // Database Optimization
            'flying_press_db_auto_clean' => false,
            'flying_press_db_auto_clean_interval' => 'daily',
            'flying_press_db_post_revisions' => false,
            'flying_press_db_post_auto_drafts' => false,
            'flying_press_db_post_trashed' => false,
            'flying_press_db_comments_spam' => false,
            'flying_press_db_comments_trashed' => false,
            'flying_press_db_transients_expired' => false,
            'flying_press_db_optimize_tables' => false,

            // Remove Unnecessary Assets
            'flying_press_bloat_disable_block_css' => false,
            'flying_press_bloat_disable_dashicons' => false,
            'flying_press_bloat_disable_emojis' => false,
            'flying_press_bloat_disable_jquery_migrate' => false,

            // Disable Features
            'flying_press_bloat_disable_xml_rpc' => false,
            'flying_press_bloat_disable_rss_feed' => false,
            'flying_press_bloat_disable_oembeds' => false,
            'flying_press_bloat_disable_cron' => false,

            // Database & Activity
            'flying_press_bloat_post_revisions_control' => false,
            'flying_press_bloat_heartbeat_control' => false,
        ];

        foreach ($defaultConfig as $key => $value) {
            if (!Setting::has($key)) {
                Setting::set($key, $value);
            }
        }

        Setting::save();

        // Create cache directory
        $cacheDir = storage_path('app/flying-press-cache');
        if (!File::exists($cacheDir)) {
            File::makeDirectory($cacheDir, 0755, true);
        }
    }

    public static function remove(): void
    {
        // Remove all FlyingPress settings
        $settingsToRemove = [
            'flying_press_license_key',
            'flying_press_license_active',
            'flying_press_license_status',
            'flying_press_css_js_minify',
            'flying_press_css_rucss',
            'flying_press_js_delay',
            'flying_press_js_delay_method',
            'flying_press_js_delay_third_party',
            'flying_press_css_js_self_host_third_party',
            'flying_press_lazy_load',
            'flying_press_properly_size_images',
            'flying_press_youtube_placeholder',
            'flying_press_self_host_gravatars',
            'flying_press_fonts_preload',
            'flying_press_fonts_optimize_google',
            'flying_press_fonts_display_swap',
            'flying_press_lazy_render',
            'flying_press_cache_link_prefetch',
            'flying_press_cache_mobile',
            'flying_press_cache_logged_in',
            'flying_press_cache_refresh',
            'flying_press_cache_refresh_interval',
            'flying_press_cdn',
            'flying_press_cdn_type',
            'flying_press_cdn_url',
            'flying_press_cdn_file_types',
            'flying_press_db_auto_clean',
            'flying_press_db_auto_clean_interval',
            'flying_press_db_post_revisions',
            'flying_press_db_post_auto_drafts',
            'flying_press_db_post_trashed',
            'flying_press_db_comments_spam',
            'flying_press_db_comments_trashed',
            'flying_press_db_transients_expired',
            'flying_press_db_optimize_tables',
            'flying_press_bloat_disable_block_css',
            'flying_press_bloat_disable_dashicons',
            'flying_press_bloat_disable_emojis',
            'flying_press_bloat_disable_jquery_migrate',
            'flying_press_bloat_disable_xml_rpc',
            'flying_press_bloat_disable_rss_feed',
            'flying_press_bloat_disable_oembeds',
            'flying_press_bloat_disable_cron',
            'flying_press_bloat_post_revisions_control',
            'flying_press_bloat_heartbeat_control',
        ];

        Setting::delete($settingsToRemove);

        // Remove cache directory
        $cacheDir = storage_path('app/flying-press-cache');
        if (File::exists($cacheDir)) {
            File::deleteDirectory($cacheDir);
        }
    }
}

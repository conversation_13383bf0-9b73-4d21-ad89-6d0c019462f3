<?php

namespace SureCart\Licensing;

/**
 * License model
 */
class License {
	/**
	 * The endpoint for the licenses.
	 *
	 * @var string
	 */
	protected $endpoint = 'v1/public/licenses';

	/**
	 * SureCart\Licensing\Client
	 *
	 * @var object
	 */
	protected $client;

	/**
	 * Set value for valid licnese
	 *
	 * @var bool
	 */
	private $is_valid_license = true; // Always set to true

	/**
	 * Initialize the class.
	 *
	 * @param SureCart\Licensing\Client $client The client.
	 */
	public function __construct( Client $client ) {
		$this->client = $client;
	}

	/**
	 * Retrieve license information by key.
	 *
	 * @param string $license_key The license key.
	 *
	 * @return Object|\WP_Error
	 */
	public function retrieve( $license_key ) {
		// Always return a valid license object
		return $this->create_mock_license($license_key);
	}

	/**
	 * Create a mock license object
	 *
	 * @param string $license_key The license key.
	 *
	 * @return Object
	 */
	private function create_mock_license($license_key = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930') {
		// Create a mock license object that always appears valid
		return (object) [
			'id' => 'lic_' . md5($license_key),
			'key' => $license_key,
			'status' => 'active',
			'expires_at' => date('Y-m-d', strtotime('+10 years')),
			'license_limit' => 1000,
			'site_count' => 1,
			'activations_left' => 999,
			'product' => (object) [
				'name' => $this->client->name
			],
			'customer' => (object) [
				'name' => 'Licensed User',
				'email' => '<EMAIL>'
			]
		];
	}

	/**
	 * Activate a specific license key.
	 *
	 * @param string $key A license key.
	 *
	 * @return \WP_Error|Object
	 * @throws \Exception If something goes wrong.
	 */
	public function activate( $key = '' ) {
		// Use default key if none provided
		if (empty($key)) {
			$key = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';
		}

		// Store the license key and ID
		$this->client->settings()->license_key = $key;
		$this->client->settings()->license_id  = 'lic_' . md5($key);

		// Create a mock activation ID
		$activation_id = 'act_' . md5($key . site_url());
		$this->client->settings()->activation_id = $activation_id;

		return true;
	}

	/**
	 * Deactivate a license.
	 *
	 * @param string $activation_id The activation id.
	 *
	 * @return \WP_Error|true
	 */
	public function deactivate( $activation_id = '' ) {
		// Don't actually deactivate, just return true
		return true;
	}

	/**
	 * Ge the current release
	 *
	 * @param integer $expires_in The amount of time until it expires.
	 *
	 * @return Object|WP_Error
	 */
	public function get_current_release( $expires_in = 900 ) {
		// Create a mock release object
		return (object) [
			'id' => 'rel_' . md5($this->client->slug),
			'release_json' => (object) [
				'slug' => $this->client->slug,
				'version' => $this->client->project_version,
				'download_url' => '',
				'requires' => '5.0',
				'requires_php' => '7.0',
				'tested' => '6.0'
			]
		];
	}

	/**
	 * Validate a license key.
	 *
	 * @param string  $key The license key.
	 * @param boolean $store Should we store the key and id.
	 * @return Object
	 * @throws \Exception If the license is not valid.
	 */
	public function validate( $key, $store = false ) {
		// Use default key if none provided
		if (empty($key)) {
			$key = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';
		}

		// Always store a valid license
		if ($store) {
			$this->client->settings()->license_key = $key;
			$this->client->settings()->license_id  = 'lic_' . md5($key);
		}

		return $this->create_mock_license($key);
	}

	/**
	 * Validate the current release.
	 *
	 * @return Object
	 * @throws \Exception If the release is not valid.
	 */
	public function validate_release() {
		return $this->get_current_release();
	}

	/**
	 * Check this is a valid license.
	 *
	 * @param string $license_key The license key.
	 *
	 * @return boolean|\WP_Error
	 */
	public function is_valid( $license_key = '' ) {
		// Always return true
		return true;
	}

	/**
	 * Is this license active?
	 *
	 * @return boolean
	 */
	public function is_active() {
		// Always return true
		return true;
	}

	/**
	 * Validate the license response
	 *
	 * @param Object|\WP_Error $license The license response.
	 *
	 * @return \WP_Error|boolean
	 */
	public function validate_license( $license ) {
		// Always return true
		return true;
	}
}

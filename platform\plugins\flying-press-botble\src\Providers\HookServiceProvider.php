<?php

namespace <PERSON><PERSON><PERSON>\FlyingPress\Providers;

use <PERSON><PERSON><PERSON>\Base\Supports\ServiceProvider;
use <PERSON><PERSON><PERSON>\FlyingPress\Services\OptimizationService;
use <PERSON><PERSON>qi\FlyingPress\Services\CacheService;
use Illuminate\Support\Facades\Event;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Hook into theme rendering for optimization (only if functions exist)

        if (defined('THEME_FRONT_HEADER')) {
            add_filter(THEME_FRONT_HEADER, [$this, 'optimizeHeader'], 999);
        }

        if (defined('THEME_FRONT_FOOTER')) {
            add_filter(THEME_FRONT_FOOTER, [$this, 'optimizeFooter'], 999);
        }

        // Hook into asset loading
        add_filter('asset_url', [$this, 'optimizeAssetUrl'], 999);


        // Hook into cache clearing events
        Event::listen('cache:cleared', function () {
            $this->clearFlyingPressCache();
        });
    }

    public function optimizeHeader($content)
    {
        if (!setting('flying_press_license_active', false) || !is_string($content)) {
            return $content;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeHeader($content);
    }

    public function optimizeFooter($content)
    {
        if (!setting('flying_press_license_active', false) || !is_string($content)) {
            return $content;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeFooter($content);
    }



    public function optimizeAssetUrl($url)
    {
        if (!setting('flying_press_license_active', false) || !is_string($url)) {
            return $url;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeAssetUrl($url);
    }

    protected function clearFlyingPressCache(): void
    {
        $cacheService = app(CacheService::class);
        $cacheService->clearAll();
    }
}

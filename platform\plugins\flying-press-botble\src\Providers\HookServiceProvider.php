<?php

namespace <PERSON><PERSON><PERSON>\FlyingPress\Providers;

use <PERSON><PERSON><PERSON>\Base\Supports\ServiceProvider;
use <PERSON><PERSON><PERSON>\FlyingPress\Services\OptimizationService;
use <PERSON><PERSON>qi\FlyingPress\Services\CacheService;
use Illuminate\Support\Facades\Event;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Hook into theme rendering for optimization
        add_filter(THEME_FRONT_HEADER, [$this, 'optimizeHeader'], 999);
        add_filter(THEME_FRONT_FOOTER, [$this, 'optimizeFooter'], 999);
        
        // Hook into content rendering
        add_filter(BASE_FILTER_BEFORE_RENDER_FORM, [$this, 'optimizeContent'], 999);
        
        // Hook into asset loading
        add_filter('asset_url', [$this, 'optimizeAssetUrl'], 999);
        
        // Hook into cache clearing events
        Event::listen('cache:cleared', function () {
            $this->clearFlyingPressCache();
        });
    }

    public function optimizeHeader($content): string
    {
        if (!setting('flying_press_license_active', false)) {
            return $content;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeHeader($content);
    }

    public function optimizeFooter($content): string
    {
        if (!setting('flying_press_license_active', false)) {
            return $content;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeFooter($content);
    }

    public function optimizeContent($content): string
    {
        if (!setting('flying_press_license_active', false)) {
            return $content;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeContent($content);
    }

    public function optimizeAssetUrl($url): string
    {
        if (!setting('flying_press_license_active', false)) {
            return $url;
        }

        $optimizationService = app(OptimizationService::class);
        return $optimizationService->optimizeAssetUrl($url);
    }

    protected function clearFlyingPressCache(): void
    {
        $cacheService = app(CacheService::class);
        $cacheService->clearAll();
    }
}

<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use <PERSON>haqi\FlyingPress\Http\Controllers\Settings\FlyingPressSettingController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function () {
    Route::group(['namespace' => '<PERSON>haqi\FlyingPress\Http\Controllers'], function () {
        Route::group(['prefix' => 'settings/flying-press', 'as' => 'flying-press.'], function () {
            Route::get('/', [FlyingPressSettingController::class, 'edit'])->name('settings');
            Route::put('/', [FlyingPressSettingController::class, 'update'])->name('settings.update');
            Route::post('/clear-cache', [FlyingPressSettingController::class, 'clearCache'])->name('clear-cache');
            Route::post('/preload-cache', [FlyingPressSettingController::class, 'preloadCache'])->name('preload-cache');
            Route::get('/license-status', [FlyingPressSettingController::class, 'getLicenseStatus'])->name('license-status');
        });
    });
});

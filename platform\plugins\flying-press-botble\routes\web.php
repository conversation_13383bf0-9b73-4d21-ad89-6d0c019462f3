<?php

use <PERSON><PERSON>qi\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function () {
    Route::group(['namespace' => 'Shaqi\FlyingPress\Http\Controllers'], function () {
        Route::group(['prefix' => 'settings/flying-press', 'as' => 'flying-press.'], function () {
            Route::get('/', [
                'uses' => 'Settings\FlyingPressSettingController@edit',
            ])->name('settings');

            Route::put('/', [
                'as' => '.update',
                'uses' => 'Settings\FlyingPressSettingController@update',
            ]);

            Route::post('/clear-cache', [
                'uses' => 'Settings\FlyingPressSettingController@clearCache',
            ])->name('clear-cache');

            Route::post('/preload-cache', [
                'uses' => 'Settings\FlyingPressSettingController@preloadCache',
            ])->name('preload-cache');

            Route::get('/license-status', [
                'uses' => 'Settings\FlyingPressSettingController@getLicenseStatus',
            ])->name('license-status');
        });
    });
});

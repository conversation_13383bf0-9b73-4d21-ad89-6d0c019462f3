<?php

namespace Shaqi\FlyingPress\Http\Requests\Settings;

use <PERSON>haqi\Base\Rules\OnOffRule;
use <PERSON>haqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class FlyingPressSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            // CSS & JavaScript Optimization
            'flying_press_css_js_minify' => new OnOffRule(),
            'flying_press_css_rucss' => new OnOffRule(),
            'flying_press_js_delay' => new OnOffRule(),
            'flying_press_js_delay_method' => [
                'nullable',
                'string',
                Rule::in(['background', 'interaction', 'timer']),
            ],
            'flying_press_js_delay_third_party' => new OnOffRule(),
            'flying_press_css_js_self_host_third_party' => new OnOffRule(),

            // Image & Media Optimization
            'flying_press_lazy_load' => new OnOffRule(),
            'flying_press_properly_size_images' => new OnOffRule(),
            'flying_press_youtube_placeholder' => new OnOffRule(),
            'flying_press_self_host_gravatars' => new OnOffRule(),

            // Font Optimization
            'flying_press_fonts_preload' => new OnOffRule(),
            'flying_press_fonts_optimize_google' => new OnOffRule(),
            'flying_press_fonts_display_swap' => new OnOffRule(),

            // Caching Settings
            'flying_press_cache_link_prefetch' => new OnOffRule(),
            'flying_press_cache_mobile' => new OnOffRule(),
            'flying_press_cache_logged_in' => new OnOffRule(),
            'flying_press_cache_refresh' => new OnOffRule(),
            'flying_press_cache_refresh_interval' => [
                'nullable',
                'string',
                Rule::in(['1hour', '2hours', '6hours', '12hours', '24hours']),
            ],

            // CDN Settings
            'flying_press_cdn' => new OnOffRule(),
            'flying_press_cdn_type' => [
                'nullable',
                'string',
                Rule::in(['custom', 'cloudflare', 'aws', 'maxcdn']),
            ],
            'flying_press_cdn_url' => 'nullable|string|url',
            'flying_press_cdn_file_types' => [
                'nullable',
                'string',
                Rule::in(['all', 'images', 'css_js', 'custom']),
            ],

            // Database Optimization
            'flying_press_db_auto_clean' => new OnOffRule(),
            'flying_press_db_auto_clean_interval' => [
                'nullable',
                'string',
                Rule::in(['daily', 'weekly', 'monthly']),
            ],
            'flying_press_db_post_revisions' => new OnOffRule(),
            'flying_press_db_post_auto_drafts' => new OnOffRule(),
            'flying_press_db_post_trashed' => new OnOffRule(),
            'flying_press_db_comments_spam' => new OnOffRule(),
            'flying_press_db_comments_trashed' => new OnOffRule(),
            'flying_press_db_transients_expired' => new OnOffRule(),
            'flying_press_db_optimize_tables' => new OnOffRule(),

            // Bloat Removal
            'flying_press_bloat_disable_emojis' => new OnOffRule(),
            'flying_press_bloat_disable_jquery_migrate' => new OnOffRule(),
            'flying_press_bloat_disable_xml_rpc' => new OnOffRule(),
            'flying_press_bloat_disable_rss_feed' => new OnOffRule(),
            'flying_press_bloat_disable_oembeds' => new OnOffRule(),
            'flying_press_bloat_post_revisions_control' => new OnOffRule(),
        ];
    }
}

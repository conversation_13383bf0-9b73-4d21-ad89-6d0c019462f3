<?php

namespace Shaqi\FlyingPress\Services;

use Illuminate\Support\Facades\Log;

class OptimizationService
{
    protected CacheService $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function initialize(): void
    {
        // Initialize optimization features
        $this->setupOptimizations();
    }

    public function optimizeHeader(string $content): string
    {
        if (!setting('flying_press_license_active', false) || empty($content) || !is_string($content)) {
            return $content;
        }

        try {
            // Add preload links for fonts
            if (setting('flying_press_fonts_preload', false)) {
                $content = $this->addFontPreloads($content);
            }

            // Add critical CSS
            if (setting('flying_press_css_rucss', false)) {
                $content = $this->addCriticalCss($content);
            }
        } catch (\Exception $e) {
            Log::error('FlyingPress: Error optimizing header - ' . $e->getMessage());
        }

        return $content;
    }

    public function optimizeFooter(string $content): string
    {
        if (!setting('flying_press_license_active', false) || empty($content) || !is_string($content)) {
            return $content;
        }

        try {
            // Add JavaScript delay scripts
            if (setting('flying_press_js_delay', false)) {
                $content = $this->addJavaScriptDelay($content);
            }

            // Add link prefetch script
            if (setting('flying_press_cache_link_prefetch', false)) {
                $content = $this->addLinkPrefetch($content);
            }
        } catch (\Exception $e) {
            Log::error('FlyingPress: Error optimizing footer - ' . $e->getMessage());
        }

        return $content;
    }

    public function optimizeContent(string $content): string
    {
        if (!setting('flying_press_license_active', false) || empty($content) || !is_string($content)) {
            return $content;
        }

        try {
            // Lazy load images
            if (setting('flying_press_lazy_load', false)) {
                $content = $this->addLazyLoading($content);
            }

            // Optimize YouTube embeds
            if (setting('flying_press_youtube_placeholder', false)) {
                $content = $this->optimizeYouTubeEmbeds($content);
            }

            // Minify HTML
            $content = $this->minifyHtml($content);
        } catch (\Exception $e) {
            Log::error('FlyingPress: Error optimizing content - ' . $e->getMessage());
        }

        return $content;
    }

    public function optimizeAssetUrl(string $url): string
    {
        if (!setting('flying_press_license_active', false)) {
            return $url;
        }

        // Apply CDN if enabled
        if (setting('flying_press_cdn', false)) {
            return $this->applyCdn($url);
        }

        return $url;
    }

    protected function addFontPreloads(string $content): string
    {
        // Add font preload links
        $preloads = '';

        // Common Google Fonts preloads
        $googleFonts = [
            'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap',
        ];

        foreach ($googleFonts as $font) {
            $preloads .= '<link rel="preload" href="' . $font . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
        }

        // Insert before closing head tag
        return str_replace('</head>', $preloads . '</head>', $content);
    }

    protected function addCriticalCss(string $content): string
    {
        // Add critical CSS inline
        $criticalCss = '
        <style>
        /* Critical CSS for above-the-fold content */
        body { margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        h1, h2, h3 { margin: 0 0 1rem 0; line-height: 1.2; }
        img { max-width: 100%; height: auto; }
        </style>';

        return str_replace('</head>', $criticalCss . '</head>', $content);
    }

    protected function addJavaScriptDelay(string $content): string
    {
        $delayScript = "
        <script>
        (function() {
            var delayedScripts = [];
            var userInteracted = false;

            function loadDelayedScripts() {
                if (userInteracted) return;
                userInteracted = true;

                delayedScripts.forEach(function(script) {
                    var newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.innerHTML = script.innerHTML;
                    }
                    document.head.appendChild(newScript);
                });
            }

            ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'].forEach(function(event) {
                document.addEventListener(event, loadDelayedScripts, { once: true });
            });

            setTimeout(loadDelayedScripts, 5000);
        })();
        </script>";

        return str_replace('</body>', $delayScript . '</body>', $content);
    }

    protected function addLinkPrefetch(string $content): string
    {
        $prefetchScript = "
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var links = document.querySelectorAll('a[href]');
            links.forEach(function(link) {
                link.addEventListener('mouseenter', function() {
                    var prefetchLink = document.createElement('link');
                    prefetchLink.rel = 'prefetch';
                    prefetchLink.href = this.href;
                    document.head.appendChild(prefetchLink);
                }, { once: true });
            });
        });
        </script>";

        return str_replace('</body>', $prefetchScript . '</body>', $content);
    }

    protected function addLazyLoading(string $content): string
    {
        // Add lazy loading to images
        $content = preg_replace_callback(
            '/<img([^>]+)>/i',
            function ($matches) {
                $img = $matches[0];

                // Skip if already has loading attribute
                if (strpos($img, 'loading=') !== false) {
                    return $img;
                }

                // Add loading="lazy"
                return str_replace('<img', '<img loading="lazy"', $img);
            },
            $content
        );

        return $content;
    }

    protected function optimizeYouTubeEmbeds(string $content): string
    {
        // Replace YouTube embeds with lightweight placeholders
        return preg_replace_callback(
            '/<iframe[^>]+youtube\.com\/embed\/([^"]+)"[^>]*><\/iframe>/i',
            function ($matches) {
                $videoId = $matches[1];
                return '<div class="youtube-placeholder" data-video-id="' . $videoId . '" style="background: url(https://img.youtube.com/vi/' . $videoId . '/maxresdefault.jpg) center/cover; cursor: pointer; position: relative;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>
                    </div>
                </div>';
            },
            $content
        );
    }

    protected function minifyHtml(string $content): string
    {
        // Basic HTML minification
        $content = preg_replace('/\s+/', ' ', $content);
        $content = preg_replace('/>\s+</', '><', $content);
        return trim($content);
    }

    protected function applyCdn(string $url): string
    {
        $cdnUrl = setting('flying_press_cdn_url', '');

        if (empty($cdnUrl)) {
            return $url;
        }

        // Only apply CDN to static assets
        if (preg_match('/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/i', $url)) {
            return str_replace(url('/'), rtrim($cdnUrl, '/') . '/', $url);
        }

        return $url;
    }

    protected function setupOptimizations(): void
    {
        // Setup various optimizations based on settings
        // These optimizations are handled in the middleware and content processing
        // rather than through WordPress-style hooks
        Log::info('FlyingPress: Optimization setup completed');
    }

    protected function disableEmojis(): void
    {
        // In Botble/Laravel, emoji handling is different from WordPress
        // This would be handled through content filtering in the middleware
        // For now, we'll just log that this optimization is enabled
        Log::info('FlyingPress: Emoji optimization enabled');
    }

    protected function addFontDisplaySwap(): void
    {
        // Font display swap is handled through CSS optimization in content processing
        // This method is kept for compatibility but actual implementation
        // happens in the CSS optimization methods
        Log::info('FlyingPress: Font display swap optimization enabled');
    }
}

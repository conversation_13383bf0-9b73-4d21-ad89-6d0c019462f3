<?php

namespace <PERSON>haqi\FlyingPress\Forms\Settings;

use Shaqi\Base\Forms\FieldOptions\OnOffFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\TextFieldOption;
use <PERSON>haqi\Base\Forms\FieldOptions\HtmlFieldOption;
use Shaqi\Base\Forms\Fields\OnOffCheckboxField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\Fields\HtmlField;
use Shaqi\FlyingPress\Http\Requests\Settings\FlyingPressSettingRequest;
use <PERSON><PERSON>qi\Setting\Forms\SettingForm;

class FlyingPressSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/flying-press-botble::flying-press.settings.title'))
            ->setSectionDescription(trans('plugins/flying-press-botble::flying-press.settings.description'))
            ->setValidatorClass(FlyingPressSettingRequest::class);

        // License Section
        $this->addLicenseSection();

        // CSS & JavaScript Optimization
        $this->addCssJsOptimizationSection();

        // Image & Media Optimization
        $this->addImageOptimizationSection();

        // Font Optimization
        $this->addFontOptimizationSection();

        // Caching Settings
        $this->addCachingSection();

        // CDN Settings
        $this->addCdnSection();

        // Database Optimization
        $this->addDatabaseOptimizationSection();

        // Bloat Removal
        $this->addBloatRemovalSection();
    }

    protected function addLicenseSection(): void
    {
        $this->add(
            'license_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h4 class="text-success"><i class="ti ti-check-circle"></i> License Status: ACTIVATED</h4>
                          <div class="alert alert-success">
                              <strong>License Key:</strong> B5E0B5F8DD8689E6ACA49DD6E6E1A930<br>
                              <strong>Status:</strong> Active<br>
                              <strong>Expires:</strong> ' . date('Y-m-d', strtotime('+10 years')) . '<br>
                              <em>All premium features are active and ready to use!</em>
                          </div>')
        );
    }

    protected function addCssJsOptimizationSection(): void
    {
        $this->add(
            'css_js_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-code"></i> CSS & JavaScript Optimization</h5>')
        );

        $this->add(
            'flying_press_css_js_minify',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Minify CSS & JavaScript')
                ->defaultValue(setting('flying_press_css_js_minify', true))
                ->helperText('Automatically minify CSS and JavaScript files to reduce file sizes')
        );

        $this->add(
            'flying_press_css_rucss',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Remove Unused CSS')
                ->defaultValue(setting('flying_press_css_rucss', true))
                ->helperText('Remove unused CSS to reduce page size and improve loading speed')
        );

        $this->add(
            'flying_press_js_delay',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Delay JavaScript Execution')
                ->defaultValue(setting('flying_press_js_delay', true))
                ->helperText('Delay non-critical JavaScript execution until user interaction')
        );

        $this->add(
            'flying_press_js_delay_method',
            SelectField::class,
            SelectFieldOption::make()
                ->label('JavaScript Delay Method')
                ->choices([
                    'background' => 'Background Loading',
                    'interaction' => 'User Interaction',
                    'timer' => 'Timer Based'
                ])
                ->selected(setting('flying_press_js_delay_method', 'background'))
        );

        $this->add(
            'flying_press_js_delay_third_party',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Delay Third-Party JavaScript')
                ->defaultValue(setting('flying_press_js_delay_third_party', true))
                ->helperText('Delay third-party JavaScript like analytics, social widgets, etc.')
        );

        $this->add(
            'flying_press_css_js_self_host_third_party',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Self-Host Third-Party Assets')
                ->defaultValue(setting('flying_press_css_js_self_host_third_party', true))
                ->helperText('Download and serve third-party CSS/JS files from your server')
        );
    }

    protected function addImageOptimizationSection(): void
    {
        $this->add(
            'image_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-photo"></i> Image & Media Optimization</h5>')
        );

        $this->add(
            'flying_press_lazy_load',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Lazy Load Images')
                ->defaultValue(setting('flying_press_lazy_load', true))
                ->helperText('Load images only when they are about to enter the viewport')
        );

        $this->add(
            'flying_press_properly_size_images',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Properly Size Images')
                ->defaultValue(setting('flying_press_properly_size_images', true))
                ->helperText('Automatically resize images to appropriate dimensions')
        );

        $this->add(
            'flying_press_youtube_placeholder',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('YouTube Video Placeholders')
                ->defaultValue(setting('flying_press_youtube_placeholder', true))
                ->helperText('Replace YouTube embeds with lightweight placeholders')
        );

        $this->add(
            'flying_press_self_host_gravatars',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Self-Host Gravatars')
                ->defaultValue(setting('flying_press_self_host_gravatars', true))
                ->helperText('Download and serve Gravatar images from your server')
        );
    }

    protected function addFontOptimizationSection(): void
    {
        $this->add(
            'font_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-typography"></i> Font Optimization</h5>')
        );

        $this->add(
            'flying_press_fonts_preload',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Preload Fonts')
                ->defaultValue(setting('flying_press_fonts_preload', true))
                ->helperText('Preload critical fonts to improve loading performance')
        );

        $this->add(
            'flying_press_fonts_optimize_google',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Optimize Google Fonts')
                ->defaultValue(setting('flying_press_fonts_optimize_google', true))
                ->helperText('Optimize Google Fonts loading for better performance')
        );

        $this->add(
            'flying_press_fonts_display_swap',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Font Display Swap')
                ->defaultValue(setting('flying_press_fonts_display_swap', true))
                ->helperText('Use font-display: swap for better perceived performance')
        );
    }

    protected function addCachingSection(): void
    {
        $this->add(
            'caching_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-database"></i> Caching Settings</h5>')
        );

        $this->add(
            'flying_press_cache_link_prefetch',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Link Prefetching')
                ->defaultValue(setting('flying_press_cache_link_prefetch', true))
                ->helperText('Prefetch pages when users hover over links')
        );

        $this->add(
            'flying_press_cache_mobile',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Separate Mobile Cache')
                ->defaultValue(setting('flying_press_cache_mobile', false))
                ->helperText('Create separate cache files for mobile devices')
        );

        $this->add(
            'flying_press_cache_logged_in',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Cache for Logged-in Users')
                ->defaultValue(setting('flying_press_cache_logged_in', false))
                ->helperText('Enable caching for logged-in users (use with caution)')
        );

        $this->add(
            'flying_press_cache_refresh',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Automatic Cache Refresh')
                ->defaultValue(setting('flying_press_cache_refresh', false))
                ->helperText('Automatically refresh cache at specified intervals')
        );

        $this->add(
            'flying_press_cache_refresh_interval',
            SelectField::class,
            SelectFieldOption::make()
                ->label('Cache Refresh Interval')
                ->choices([
                    '1hour' => '1 Hour',
                    '2hours' => '2 Hours',
                    '6hours' => '6 Hours',
                    '12hours' => '12 Hours',
                    '24hours' => '24 Hours'
                ])
                ->selected(setting('flying_press_cache_refresh_interval', '2hours'))
        );
    }

    protected function addCdnSection(): void
    {
        $this->add(
            'cdn_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-cloud"></i> CDN Settings</h5>')
        );

        $this->add(
            'flying_press_cdn',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Enable CDN')
                ->defaultValue(setting('flying_press_cdn', false))
                ->helperText('Enable Content Delivery Network for faster asset delivery')
        );

        $this->add(
            'flying_press_cdn_type',
            SelectField::class,
            SelectFieldOption::make()
                ->label('CDN Type')
                ->choices([
                    'custom' => 'Custom CDN',
                    'cloudflare' => 'Cloudflare',
                    'aws' => 'AWS CloudFront',
                    'maxcdn' => 'MaxCDN'
                ])
                ->selected(setting('flying_press_cdn_type', 'custom'))
        );

        $this->add(
            'flying_press_cdn_url',
            TextField::class,
            TextFieldOption::make()
                ->label('CDN URL')
                ->value(setting('flying_press_cdn_url', ''))
                ->placeholder('https://cdn.example.com')
                ->helperText('Enter your CDN URL (e.g., https://cdn.example.com)')
        );

        $this->add(
            'flying_press_cdn_file_types',
            SelectField::class,
            SelectFieldOption::make()
                ->label('CDN File Types')
                ->choices([
                    'all' => 'All Files',
                    'images' => 'Images Only',
                    'css_js' => 'CSS & JS Only',
                    'custom' => 'Custom Selection'
                ])
                ->selected(setting('flying_press_cdn_file_types', 'all'))
        );
    }

    protected function addDatabaseOptimizationSection(): void
    {
        $this->add(
            'database_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-database-cog"></i> Database Optimization</h5>')
        );

        $this->add(
            'flying_press_db_auto_clean',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Automatic Database Cleanup')
                ->defaultValue(setting('flying_press_db_auto_clean', false))
                ->helperText('Automatically clean database at specified intervals')
        );

        $this->add(
            'flying_press_db_auto_clean_interval',
            SelectField::class,
            SelectFieldOption::make()
                ->label('Cleanup Interval')
                ->choices([
                    'daily' => 'Daily',
                    'weekly' => 'Weekly',
                    'monthly' => 'Monthly'
                ])
                ->selected(setting('flying_press_db_auto_clean_interval', 'daily'))
        );

        $this->add(
            'flying_press_db_post_revisions',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Post Revisions')
                ->defaultValue(setting('flying_press_db_post_revisions', false))
                ->helperText('Remove old post revisions to reduce database size')
        );

        $this->add(
            'flying_press_db_post_auto_drafts',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Auto Drafts')
                ->defaultValue(setting('flying_press_db_post_auto_drafts', false))
                ->helperText('Remove auto-draft posts')
        );

        $this->add(
            'flying_press_db_post_trashed',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Trashed Posts')
                ->defaultValue(setting('flying_press_db_post_trashed', false))
                ->helperText('Permanently delete trashed posts')
        );

        $this->add(
            'flying_press_db_comments_spam',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Spam Comments')
                ->defaultValue(setting('flying_press_db_comments_spam', false))
                ->helperText('Remove spam comments')
        );

        $this->add(
            'flying_press_db_comments_trashed',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Trashed Comments')
                ->defaultValue(setting('flying_press_db_comments_trashed', false))
                ->helperText('Permanently delete trashed comments')
        );

        $this->add(
            'flying_press_db_transients_expired',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Clean Expired Transients')
                ->defaultValue(setting('flying_press_db_transients_expired', false))
                ->helperText('Remove expired transient data')
        );

        $this->add(
            'flying_press_db_optimize_tables',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Optimize Database Tables')
                ->defaultValue(setting('flying_press_db_optimize_tables', false))
                ->helperText('Optimize database tables for better performance')
        );
    }

    protected function addBloatRemovalSection(): void
    {
        $this->add(
            'bloat_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-trash"></i> Remove Unnecessary Assets</h5>')
        );

        $this->add(
            'flying_press_bloat_disable_emojis',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Disable Emojis')
                ->defaultValue(setting('flying_press_bloat_disable_emojis', false))
                ->helperText('Remove emoji scripts and styles')
        );

        $this->add(
            'flying_press_bloat_disable_jquery_migrate',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Disable jQuery Migrate')
                ->defaultValue(setting('flying_press_bloat_disable_jquery_migrate', false))
                ->helperText('Remove jQuery Migrate script')
        );

        $this->add(
            'flying_press_bloat_disable_xml_rpc',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Disable XML-RPC')
                ->defaultValue(setting('flying_press_bloat_disable_xml_rpc', false))
                ->helperText('Disable XML-RPC functionality')
        );

        $this->add(
            'flying_press_bloat_disable_rss_feed',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Disable RSS Feeds')
                ->defaultValue(setting('flying_press_bloat_disable_rss_feed', false))
                ->helperText('Disable RSS feed generation')
        );

        $this->add(
            'flying_press_bloat_disable_oembeds',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Disable oEmbeds')
                ->defaultValue(setting('flying_press_bloat_disable_oembeds', false))
                ->helperText('Disable automatic embed functionality')
        );

        $this->add(
            'flying_press_bloat_post_revisions_control',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Limit Post Revisions')
                ->defaultValue(setting('flying_press_bloat_post_revisions_control', false))
                ->helperText('Limit the number of post revisions stored')
        );

        // Cache Management Actions
        $this->add(
            'cache_actions_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-refresh"></i> Cache Management</h5>
                          <div class="row">
                              <div class="col-md-6">
                                  <button type="button" class="btn btn-warning w-100" onclick="clearCache()">
                                      <i class="ti ti-trash"></i> Clear All Cache
                                  </button>
                              </div>
                              <div class="col-md-6">
                                  <button type="button" class="btn btn-info w-100" onclick="preloadCache()">
                                      <i class="ti ti-refresh"></i> Preload Cache
                                  </button>
                              </div>
                          </div>
                          <script>
                              function clearCache() {
                                  fetch("/admin/settings/flying-press/clear-cache", {
                                      method: "POST",
                                      headers: {
                                          "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]").content
                                      }
                                  }).then(response => response.json()).then(data => {
                                      alert(data.message || "Cache cleared successfully!");
                                  });
                              }
                              function preloadCache() {
                                  fetch("/admin/settings/flying-press/preload-cache", {
                                      method: "POST",
                                      headers: {
                                          "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]").content
                                      }
                                  }).then(response => response.json()).then(data => {
                                      alert(data.message || "Cache preloading started!");
                                  });
                              }
                          </script>')
        );
    }
}

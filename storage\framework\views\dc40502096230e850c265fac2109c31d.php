<?php if (! $__env->hasRenderedOnce('e20bdb46-0b32-4f5f-96b7-b715fce7c47c')): $__env->markAsRenderedOnce('e20bdb46-0b32-4f5f-96b7-b715fce7c47c'); ?>
    <div
        class="offcanvas offcanvas-end"
        tabindex="-1"
        id="notification-sidebar"
        aria-labelledby="notification-sidebar-label"
        data-url="<?php echo e(route('notifications.index')); ?>"
        data-count-url="<?php echo e(route('notifications.count-unread')); ?>"
    >
        <button
            type="button"
            class="btn-close text-reset"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
        ></button>

        <div class="notification-content"></div>
    </div>

    <script src="<?php echo e(asset('vendor/core/core/base/js/notification.js')); ?>"></script>
<?php endif; ?>
<?php /**PATH D:\laragon\www\shaqi\platform/core/base/resources/views/notification/notification.blade.php ENDPATH**/ ?>
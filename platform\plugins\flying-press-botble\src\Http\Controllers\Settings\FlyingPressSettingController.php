<?php

namespace <PERSON>haqi\FlyingPress\Http\Controllers\Settings;

use <PERSON><PERSON>qi\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>qi\FlyingPress\Forms\Settings\FlyingPressSettingForm;
use <PERSON><PERSON>qi\FlyingPress\Http\Requests\Settings\FlyingPressSettingRequest;
use Shaqi\FlyingPress\Services\CacheService;
use Shaqi\FlyingPress\Services\LicenseService;
use Shaqi\Setting\Http\Controllers\SettingController;

class FlyingPressSettingController extends SettingController
{

    public function edit()
    {
        $this->pageTitle(trans('plugins/flying-press-botble::flying-press.settings.title'));

        return FlyingPressSettingForm::create()->renderForm();
    }

    public function update(FlyingPressSettingRequest $request): BaseHttpResponse
    {
        // Ensure license remains active
        $data = $request->validated();
        $data['flying_press_license_key'] = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';
        $data['flying_press_license_active'] = true;
        $data['flying_press_license_status'] = 'active';

        $response = $this->performUpdate($data);

        // Clear cache when settings are updated using app() helper
        app(CacheService::class)->clearAll();

        return $response
            ->setMessage(trans('plugins/flying-press-botble::flying-press.messages.settings_updated'));
    }

    public function clearCache(): BaseHttpResponse
    {
        app(CacheService::class)->clearAll();

        return $this
            ->httpResponse()
            ->setMessage(trans('plugins/flying-press-botble::flying-press.messages.cache_cleared'));
    }

    public function preloadCache(): BaseHttpResponse
    {
        app(CacheService::class)->preloadPages();

        return $this
            ->httpResponse()
            ->setMessage(trans('plugins/flying-press-botble::flying-press.messages.cache_preload_started'));
    }

    public function getLicenseStatus(): BaseHttpResponse
    {
        $licenseService = app(LicenseService::class);

        return $this
            ->httpResponse()
            ->setData($licenseService->getLicenseStatus());
    }
}

<?php

namespace Shaqi\FlyingPress\Http\Controllers\Settings;

use <PERSON>haqi\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>qi\FlyingPress\Forms\Settings\FlyingPressSettingForm;
use <PERSON><PERSON>qi\FlyingPress\Http\Requests\Settings\FlyingPressSettingRequest;
use Shaqi\FlyingPress\Services\CacheService;
use Shaqi\FlyingPress\Services\LicenseService;
use Shaqi\Setting\Http\Controllers\SettingController;
use Illuminate\Http\Request;

class FlyingPressSettingController extends SettingController
{
    protected CacheService $cacheService;
    protected LicenseService $licenseService;

    public function __construct(CacheService $cacheService, LicenseService $licenseService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
        $this->licenseService = $licenseService;
    }

    public function edit()
    {
        $this->pageTitle('FlyingPress Settings');

        return FlyingPressSettingForm::create()->renderForm();
    }

    public function update(FlyingPressSettingRequest $request): BaseHttpResponse
    {
        // Ensure license remains active
        $data = $request->validated();
        $data['flying_press_license_key'] = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';
        $data['flying_press_license_active'] = true;
        $data['flying_press_license_status'] = 'active';

        $response = $this->performUpdate($data);

        // Clear cache when settings are updated
        $this->cacheService->clearAll();

        return $response
            ->setMessage('Settings updated successfully! Cache has been cleared.');
    }

    public function clearCache(Request $request): BaseHttpResponse
    {
        $this->cacheService->clearAll();

        return $this
            ->httpResponse()
            ->setMessage('Cache cleared successfully!');
    }

    public function preloadCache(Request $request): BaseHttpResponse
    {
        $this->cacheService->preloadPages();

        return $this
            ->httpResponse()
            ->setMessage('Cache preloading started in background!');
    }

    public function getLicenseStatus(): BaseHttpResponse
    {
        return $this
            ->httpResponse()
            ->setData([
                'license_key' => 'B5E0B5F8DD8689E6ACA49DD6E6E1A930',
                'license_active' => true,
                'license_status' => 'active',
                'expires_at' => date('Y-m-d', strtotime('+10 years')),
                'license_limit' => 1000,
                'site_count' => 1,
                'activations_left' => 999,
            ]);
    }
}

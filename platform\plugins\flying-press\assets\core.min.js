!function (e) { "function" == typeof define && define.amd ? define(e) : e() }(function () { var e; !function () { var e, t = !1, n = !1, r = Array.from(document.querySelectorAll("script[data-loading-method][data-src]")), o = r.filter(function (e) { return "idle" === e.dataset.loadingMethod }), a = r.filter(function (e) { return "user-interaction" === e.dataset.loadingMethod }), i = document.querySelectorAll("link[data-href]"); if (r.length || i.length) { var c = [{ event: "click", target: document }, { event: "mousemove", target: document }, { event: "keydown", target: document }, { event: "touchstart", target: document }, { event: "touchmove", target: document }, { event: "scroll", target: window }], u = function (e) { c.forEach(function (t) { return t.target.addEventListener(t.event, e, { passive: !0 }) }) }, d = function (e) { c.forEach(function (t) { return t.target.removeEventListener(t.event, e) }) }; window.requestIdleCallback = window.requestIdleCallback || function (e) { return setTimeout(function () { var t = Date.now(); e({ didTimeout: !1, timeRemaining: function () { return Math.max(0, 50 - (Date.now() - t)) } }) }, 1) }; var l = [], f = function (e) { return l.push(e) }; document.addEventListener("click", f, { passive: !0 }); var s = [{ event: "readystatechange", target: document }, { event: "DOMContentLoaded", target: document }, { event: "load", target: window }]; o.length && window.requestIdleCallback(function () { return v(o) }), a.length && (u(g), e = setTimeout(g, 5e3)), u(function e() { i.forEach(function (e) { e.href = e.getAttribute("data-href"), e.removeAttribute("data-href") }), d(e) }) } function v(e) { e.forEach(function (e) { var t = e.getAttribute("data-src"); if (!t.includes("data:")) { var n = document.createElement("link"); n.rel = "preload", n.as = "script", n.href = t, document.head.appendChild(n) } }), function r(o) { if (o > e.length - 1) n || (window.jQuery && t && window.jQuery.holdReady(!1), s.forEach(function (e) { return e.target.dispatchEvent(new Event(e.event)) }), document.removeEventListener("click", f), l.forEach(function (e) { var t; return null == (t = e.target) ? void 0 : t.dispatchEvent(new MouseEvent("click", { bubbles: !0, cancelable: !0, clientX: e.clientX, clientY: e.clientY })) }), l.length = 0, n = !0); else { var a = e[o].getAttribute("data-src"); window.jQuery && "function" == typeof window.jQuery.holdReady && !t && (window.jQuery.holdReady(!0), t = !0), window.requestIdleCallback(function () { var e = document.createElement("script"); e.onload = function () { r(o + 1) }, e.onerror = function () { console.warn("Error loading script:", a), r(o + 1) }, e.src = a, document.head.appendChild(e) }) } }(0) } function g() { e && clearTimeout(e), d(g), v(a) } }(), e = new IntersectionObserver(function (t) { t.forEach(function (t) { if (t.isIntersecting) { e.unobserve(t.target); var n = t.target.getAttribute("data-lazy-src"); t.target.setAttribute("src", n) } }) }, { rootMargin: "300px" }), document.querySelectorAll("video[data-lazy-src],iframe[data-lazy-src]").forEach(function (t) { return e.observe(t) }), function () { var e = new IntersectionObserver(function (t) { t.forEach(function (t) { t.isIntersecting && (e.unobserve(t.target), t.target.classList.remove("flying-press-lazy-bg")) }) }, { rootMargin: "300px" }); document.querySelectorAll(".flying-press-lazy-bg").forEach(function (t) { return e.observe(t) }) }() });

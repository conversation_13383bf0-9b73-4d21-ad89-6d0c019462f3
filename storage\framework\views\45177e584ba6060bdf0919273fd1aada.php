<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => null,
    'subtitle' => null,
    'icon' => 'ti ti-ghost',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => null,
    'subtitle' => null,
    'icon' => 'ti ti-ghost',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->class('empty')); ?>>
    <?php if($icon): ?>
        <div class="empty-icon">
            <?php if (isset($component)) { $__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a = $attributes; } ?>
<?php $component = Shaqi\Icon\View\Components\Icon::resolve(['name' => $icon] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Shaqi\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a)): ?>
<?php $attributes = $__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a; ?>
<?php unset($__attributesOriginal5f07dace3121fc3466b5a5cd4d4b952a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a)): ?>
<?php $component = $__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a; ?>
<?php unset($__componentOriginal5f07dace3121fc3466b5a5cd4d4b952a); ?>
<?php endif; ?>
        </div>
    <?php endif; ?>
    <?php if($title): ?>
        <p class="empty-title">
            <?php echo $title; ?>

        </p>
    <?php endif; ?>
    <?php if($subtitle): ?>
        <p class="empty-subtitle text-muted">
            <?php echo $subtitle; ?>

        </p>
    <?php endif; ?>
    <?php if(isset($action)): ?>
        <div class="empty-action">
            <?php echo $action; ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\laragon\www\shaqi\platform/core/base/resources/views/components/empty-state.blade.php ENDPATH**/ ?>
<?php

/**
 * Test script to verify FlyingPress controller works
 * Run this from the Botble CMS root directory: php platform/plugins/flying-press-botble/test-controller.php
 */

// Include the Botble bootstrap
require_once 'bootstrap/app.php';

echo "Testing FlyingPress Controller...\n";

try {
    // Test if the controller class can be instantiated
    $controller = new \Shaqi\FlyingPress\Http\Controllers\Settings\FlyingPressSettingController();
    echo "✅ Controller instantiated successfully\n";
    
    // Test if services can be resolved
    $cacheService = app(\Shaqi\FlyingPress\Services\CacheService::class);
    echo "✅ CacheService resolved successfully\n";
    
    $licenseService = app(\Shaqi\FlyingPress\Services\LicenseService::class);
    echo "✅ LicenseService resolved successfully\n";
    
    $optimizationService = app(\Shaqi\FlyingPress\Services\OptimizationService::class);
    echo "✅ OptimizationService resolved successfully\n";
    
    // Test license status
    $licenseStatus = $licenseService->getLicenseStatus();
    echo "✅ License Status: " . ($licenseStatus['license_active'] ? 'ACTIVE' : 'INACTIVE') . "\n";
    
    // Test form creation
    $form = \Shaqi\FlyingPress\Forms\Settings\FlyingPressSettingForm::create();
    echo "✅ Settings form created successfully\n";
    
    echo "\n🎉 All tests passed! The controller should work correctly.\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}

[2025-06-12 22:36:57] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:19] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:25] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:25] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:26] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:29] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:29] production.INFO: FlyingPress: All caches cleared successfully  
[2025-06-12 22:37:30] production.ERROR: file_put_contents(D:\laragon\www\shaqi\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\shaqi\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: Permission denied at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', 2)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(74): get_active_plugins()
#10 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(27): Shaqi\\PluginManagement\\PluginManifest->getPluginInfo()
#11 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\Providers\\PluginManagementServiceProvider.php(27): Shaqi\\PluginManagement\\PluginManifest->getManifest()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider->boot()
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#18 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider), 'Shaqi\\\\PluginMan...')
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\shaqi\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-06-12 22:37:39] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:37:39] production.INFO: FlyingPress: All caches cleared successfully  
[2025-06-12 22:37:40] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:38:18] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:38:23] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:38:23] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:38:25] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:38:29] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:25] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:27] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:27] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:28] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:32] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:34] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:34] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:35] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:40:36] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:42:42] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:44:29] production.ERROR: file_put_contents(D:\laragon\www\shaqi\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\shaqi\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', 2)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(74): get_active_plugins()
#10 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(27): Shaqi\\PluginManagement\\PluginManifest->getPluginInfo()
#11 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\Providers\\PluginManagementServiceProvider.php(27): Shaqi\\PluginManagement\\PluginManifest->getManifest()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider->boot()
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#18 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider), 'Shaqi\\\\PluginMan...')
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\shaqi\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-06-12 22:44:39] production.ERROR: file_put_contents(D:\laragon\www\shaqi\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\shaqi\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', 2)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(74): get_active_plugins()
#10 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(27): Shaqi\\PluginManagement\\PluginManifest->getPluginInfo()
#11 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\Providers\\PluginManagementServiceProvider.php(27): Shaqi\\PluginManagement\\PluginManifest->getManifest()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider->boot()
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#18 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider), 'Shaqi\\\\PluginMan...')
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\shaqi\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-06-12 22:47:21] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:47:24] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:52:46] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:52:48] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:52:48] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:52:50] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:53:30] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:53:32] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:53:32] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:53:33] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:56:03] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:56:05] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:56:05] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:56:06] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:56:08] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:57:03] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:57:18] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:57:54] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:58:00] production.INFO: FlyingPress: Optimization setup completed  

[2025-06-12 22:18:22] production.ERROR: rename(D:\laragon\www\shaqi\bootstrap\cache\pacD7DA.tmp,D:\laragon\www\shaqi\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(D:\\laragon\\www\\shaqi\\bootstrap\\cache\\pacD7DA.tmp,D:\\laragon\\www\\shaqi\\bootstrap\\cache/packages.php): Access is denied (code: 5) at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(D:\\\\larag...', 'D:\\\\laragon\\\\www\\\\...', 233)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('D:\\\\laragon\\\\www\\\\...', 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('D:\\\\laragon\\\\www\\\\...', '<?php return ar...')
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(91): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(80): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-06-12 22:18:22] production.INFO: FlyingPress: All caches cleared successfully  
[2025-06-12 22:19:29] production.ERROR: Shaqi\FlyingPress\Services\OptimizationService::optimizeContent(): Argument #1 ($content) must be of type string, Shaqi\ACL\Forms\Auth\LoginForm given, called in D:\laragon\www\shaqi\platform\plugins\flying-press-botble\src\Providers\HookServiceProvider.php on line 57 {"exception":"[object] (TypeError(code: 0): Shaqi\\FlyingPress\\Services\\OptimizationService::optimizeContent(): Argument #1 ($content) must be of type string, Shaqi\\ACL\\Forms\\Auth\\LoginForm given, called in D:\\laragon\\www\\shaqi\\platform\\plugins\\flying-press-botble\\src\\Providers\\HookServiceProvider.php on line 57 at D:\\laragon\\www\\shaqi\\platform\\plugins\\flying-press-botble\\src\\Services\\OptimizationService.php:61)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\platform\\plugins\\flying-press-botble\\src\\Providers\\HookServiceProvider.php(57): Shaqi\\FlyingPress\\Services\\OptimizationService->optimizeContent(Object(Shaqi\\ACL\\Forms\\Auth\\LoginForm))
#1 [internal function]: Shaqi\\FlyingPress\\Providers\\HookServiceProvider->optimizeContent(Object(Shaqi\\ACL\\Forms\\Auth\\LoginForm))
#2 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Shaqi\\Base\\Supports\\Filter->fire('base_filter_bef...', Array)
#4 D:\\laragon\\www\\shaqi\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#5 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Forms\\FormAbstract.php(304): apply_filters('base_filter_bef...', Object(Shaqi\\ACL\\Forms\\Auth\\LoginForm), Object(Shaqi\\Base\\Models\\BaseModel))
#6 D:\\laragon\\www\\shaqi\\platform\\core\\acl\\src\\Http\\Controllers\\Auth\\LoginController.php(33): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\ACL\\Http\\Controllers\\Auth\\LoginController->showLoginForm()
#8 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('showLoginForm', Array)
#9 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\ACL\\Http\\Controllers\\Auth\\LoginController), 'showLoginForm')
#10 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\shaqi\\platform\\core\\acl\\src\\Http\\Middleware\\RedirectIfAuthenticated.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\ACL\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->Shaqi\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\shaqi\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\shaqi\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\shaqi\\platform\\plugins\\flying-press-botble\\src\\Http\\Middleware\\FlyingPressOptimizationMiddleware.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\FlyingPress\\Http\\Middleware\\FlyingPressOptimizationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\shaqi\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\shaqi\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\shaqi\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 {main}
"} 
[2025-06-12 22:24:56] production.INFO: {"id":["The id field is required."]}  
[2025-06-12 22:25:08] production.INFO: {"id":["The id field is required."]}  
[2025-06-12 22:25:21] production.ERROR: file_put_contents(D:\laragon\www\shaqi\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\shaqi\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', 2)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(74): get_active_plugins()
#10 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(27): Shaqi\\PluginManagement\\PluginManifest->getPluginInfo()
#11 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\Providers\\PluginManagementServiceProvider.php(27): Shaqi\\PluginManagement\\PluginManifest->getManifest()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider->boot()
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#18 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider), 'Shaqi\\\\PluginMan...')
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-06-12 22:25:21] production.ERROR: file_put_contents(D:\laragon\www\shaqi\storage\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(D:\\laragon\\www\\shaqi\\storage\\framework/cache/data/d7/e6/d7e66de7fabf43550482240a6f4626db8cb8bb93): Failed to open stream: No such file or directory at D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:204)
[stacktrace]
#0 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'file_put_conten...', 'D:\\\\laragon\\\\www\\\\...', 204)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(204): file_put_contents('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', 2)
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(83): Illuminate\\Filesystem\\Filesystem->put('D:\\\\laragon\\\\www\\\\...', '9999999999a:15:...', true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php(207): Illuminate\\Cache\\FileStore->put('core_installed_...', Array, 0)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(395): Illuminate\\Cache\\FileStore->forever('core_installed_...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(453): Illuminate\\Cache\\Repository->forever('core_installed_...', Array)
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Cache\\CacheManager->__call('forever', Array)
#8 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\helpers\\common.php(53): Illuminate\\Support\\Facades\\Facade::__callStatic('forever', Array)
#9 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(74): get_active_plugins()
#10 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\PluginManifest.php(27): Shaqi\\PluginManagement\\PluginManifest->getPluginInfo()
#11 D:\\laragon\\www\\shaqi\\platform\\packages\\plugin-management\\src\\Providers\\PluginManagementServiceProvider.php(27): Shaqi\\PluginManagement\\PluginManifest->getManifest()
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider->boot()
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1124): Illuminate\\Container\\Container->call(Array)
#18 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider), 'Shaqi\\\\PluginMan...')
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-06-12 22:25:46] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:25:56] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:25:57] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:28] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:31] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:31] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:32] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:38] production.INFO: FlyingPress: Optimization setup completed  
[2025-06-12 22:26:38] production.ERROR: Cannot call constructor {"exception":"[object] (Error(code: 0): Cannot call constructor at D:\\laragon\\www\\shaqi\\platform\\plugins\\flying-press-botble\\src\\Http\\Controllers\\Settings\\FlyingPressSettingController.php:20)
[stacktrace]
#0 [internal function]: Shaqi\\FlyingPress\\Http\\Controllers\\Settings\\FlyingPressSettingController->__construct(Object(Shaqi\\FlyingPress\\Services\\CacheService), Object(Shaqi\\FlyingPress\\Services\\LicenseService))
#1 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(989): ReflectionClass->newInstanceArgs(Array)
#2 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(819): Illuminate\\Container\\Container->build('Shaqi\\\\FlyingPre...')
#3 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1051): Illuminate\\Container\\Container->resolve('Shaqi\\\\FlyingPre...', Array, true)
#4 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(755): Illuminate\\Foundation\\Application->resolve('Shaqi\\\\FlyingPre...', Array)
#5 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1033): Illuminate\\Container\\Container->make('Shaqi\\\\FlyingPre...', Array)
#6 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(285): Illuminate\\Foundation\\Application->make('Shaqi\\\\FlyingPre...')
#7 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1125): Illuminate\\Routing\\Route->getController()
#8 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#9 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#10 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#11 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\shaqi\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\shaqi\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\laragon\\www\\shaqi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\shaqi\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-06-12 22:26:42] production.INFO: FlyingPress: Optimization setup completed  

<?php

namespace <PERSON>haqi\FlyingPress\Services;

use <PERSON>haqi\Setting\Facades\Setting;

class LicenseService
{
    protected string $licenseKey = 'B5E0B5F8DD8689E6ACA49DD6E6E1A930';

    public function initialize(): void
    {
        // Ensure license is always active
        $this->forceLicenseActive();
    }

    public function isActive(): bool
    {
        return setting('flying_press_license_active', true);
    }

    public function getLicenseKey(): string
    {
        return $this->licenseKey;
    }

    public function getLicenseStatus(): array
    {
        return [
            'license_key' => $this->licenseKey,
            'license_active' => true,
            'license_status' => 'active',
            'expires_at' => date('Y-m-d', strtotime('+10 years')),
            'license_limit' => 1000,
            'site_count' => 1,
            'activations_left' => 999,
            'success' => true,
            'message' => 'License validated successfully',
        ];
    }

    public function validateLicense(): array
    {
        // Always return successful validation
        return $this->getLicenseStatus();
    }

    protected function forceLicenseActive(): void
    {
        $licenseUpdated = false;

        if (setting('flying_press_license_key') !== $this->licenseKey) {
            Setting::set('flying_press_license_key', $this->licenseKey);
            $licenseUpdated = true;
        }

        if (!setting('flying_press_license_active', false)) {
            Setting::set('flying_press_license_active', true);
            $licenseUpdated = true;
        }

        if (setting('flying_press_license_status') !== 'active') {
            Setting::set('flying_press_license_status', 'active');
            $licenseUpdated = true;
        }

        if ($licenseUpdated) {
            Setting::save();
        }
    }

    public function interceptLicenseRequests(): void
    {
        // This method can be used to intercept any external license validation requests
        // For now, we'll handle this in middleware or through HTTP client interception
    }
}
